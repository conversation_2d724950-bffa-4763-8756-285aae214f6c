import type { APIRoute } from 'astro';
import { createCloudflareAPI, isValidDomain } from '../../../lib/cloudflare-api';

export const prerender = false;

interface CloudflareRequestBody {
  domain_id: string;
  action: 'setup' | 'remove';
  user_id?: string;
}

interface CustomDomain {
  id: string;
  domain: string;
  pages_project_name: string | null;
  cloudflare_zone_id: string | null;
  cloudflare_custom_hostname_id: string | null;
}

// POST - Setup domain with Cloudflare Pages
export const POST: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const body = await request.json() as CloudflareRequestBody;
    const { domain_id, action, user_id = 'default-user' } = body;

    if (!domain_id || !action) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain ID and action are required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details
    const domain = await db.prepare(`
      SELECT id, domain, pages_project_name, cloudflare_zone_id, cloudflare_custom_hostname_id
      FROM custom_domains
      WHERE id = ? AND user_id = ?
    `).bind(domain_id, user_id).first() as CustomDomain | null;

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain not found or access denied' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare API is configured
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Cloudflare API not configured. Please contact administrator.' 
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const cfApi = createCloudflareAPI(env);

    try {
      if (action === 'setup') {
        if (!domain.pages_project_name) {
          return new Response(JSON.stringify({
            success: false,
            error: 'Pages project name is required for domain setup'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // Add domain to Pages project using official Cloudflare Pages API
        const pagesResult = await cfApi.addDomainToPages(
          domain.domain,
          domain.pages_project_name!
        );

        // Also setup DNS records if zone exists
        const setupResult = await cfApi.setupDomainForPages(
          domain.domain,
          domain.pages_project_name!
        );

        // Update domain with Cloudflare information
        await db.prepare(`
          UPDATE custom_domains
          SET cloudflare_zone_id = ?,
              cloudflare_custom_hostname_id = ?,
              status = 'active',
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(
          setupResult.zoneId,
          pagesResult.result?.id || null,
          domain_id
        ).run();

        return new Response(JSON.stringify({
          success: true,
          message: 'Domain configured successfully with Cloudflare Pages',
          zone_id: setupResult.zoneId,
          pages_domain_id: pagesResult.result?.id,
          pages_result: pagesResult.result
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });

      } else if (action === 'remove') {
        if (!domain.pages_project_name) {
          return new Response(JSON.stringify({
            success: false,
            error: 'Pages project name is required for domain removal'
          }), {
            status: 400,
            headers: { 'Content-Type': 'application/json' }
          });
        }

        // Remove domain from Pages project using official API
        await cfApi.removeDomainFromPages(domain.domain, domain.pages_project_name!);

        // Update domain status
        await db.prepare(`
          UPDATE custom_domains
          SET status = 'pending',
              cloudflare_zone_id = NULL,
              cloudflare_custom_hostname_id = NULL,
              updated_at = CURRENT_TIMESTAMP
          WHERE id = ?
        `).bind(domain_id).run();

        return new Response(JSON.stringify({
          success: true,
          message: 'Domain removed successfully from Cloudflare Pages'
        }), {
          status: 200,
          headers: { 'Content-Type': 'application/json' }
        });

      } else {
        return new Response(JSON.stringify({
          success: false,
          error: 'Invalid action. Use "setup" or "remove".'
        }), {
          status: 400,
          headers: { 'Content-Type': 'application/json' }
        });
      }

    } catch (cfError: any) {
      console.error('Cloudflare API error:', cfError);
      
      // Update domain with error
      await db.prepare(`
        UPDATE custom_domains 
        SET status = 'failed', error_message = ?, updated_at = CURRENT_TIMESTAMP
        WHERE id = ?
      `).bind(cfError.message || 'Cloudflare API error', domain_id).run();

      return new Response(JSON.stringify({
        success: false,
        error: 'Cloudflare configuration failed',
        details: cfError.message
      }), {
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error in Cloudflare integration:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to process Cloudflare request' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};

// GET - Check Cloudflare status for domain
export const GET: APIRoute = async ({ request, locals }) => {
  try {
    // @ts-ignore
    const env = locals.runtime.env;
    // @ts-ignore  
    const db = env.DB as D1Database;

    if (!db) {
      return new Response(JSON.stringify({ error: "D1 database not configured." }), { 
        status: 500,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    const url = new URL(request.url);
    const domainId = url.searchParams.get('domain_id');
    const userId = url.searchParams.get('user_id') || 'default-user';

    if (!domainId) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain ID is required' 
      }), {
        status: 400,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Get domain details
    const domain = await db.prepare(`
      SELECT id, domain, cloudflare_zone_id, cloudflare_custom_hostname_id
      FROM custom_domains
      WHERE id = ? AND user_id = ?
    `).bind(domainId, userId).first() as CustomDomain | null;

    if (!domain) {
      return new Response(JSON.stringify({ 
        success: false,
        error: 'Domain not found or access denied' 
      }), {
        status: 404,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    // Check if Cloudflare API is configured
    if (!env.CLOUDFLARE_API_TOKEN || !env.CLOUDFLARE_ACCOUNT_ID) {
      return new Response(JSON.stringify({ 
        success: true,
        cloudflare_configured: false,
        message: 'Cloudflare API not configured' 
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

    try {
      const cfApi = createCloudflareAPI(env);
      
      // Check if domain's zone exists in Cloudflare
      const zone = await cfApi.getZoneByDomain(domain.domain);
      
      let dnsRecords = null;
      if (zone && domain.cloudflare_zone_id) {
        // Get DNS records for the domain
        const records = await cfApi.listDNSRecords(zone.id, undefined, domain.domain);
        dnsRecords = records.result || [];
      }

      return new Response(JSON.stringify({
        success: true,
        cloudflare_configured: true,
        zone_exists: !!zone,
        zone_id: zone?.id,
        dns_records: dnsRecords,
        domain: {
          id: domain.id,
          domain: domain.domain,
          cloudflare_zone_id: domain.cloudflare_zone_id,
          cloudflare_custom_hostname_id: domain.cloudflare_custom_hostname_id
        }
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });

    } catch (cfError: any) {
      console.error('Cloudflare API error:', cfError);
      
      return new Response(JSON.stringify({
        success: true,
        cloudflare_configured: true,
        error: 'Failed to check Cloudflare status',
        details: cfError.message
      }), {
        status: 200,
        headers: { 'Content-Type': 'application/json' }
      });
    }

  } catch (error) {
    console.error('Error checking Cloudflare status:', error);
    return new Response(JSON.stringify({ 
      success: false,
      error: 'Failed to check Cloudflare status' 
    }), {
      status: 500,
      headers: { 'Content-Type': 'application/json' }
    });
  }
};
