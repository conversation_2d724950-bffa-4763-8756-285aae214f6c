/**
 * Test script for Cloudflare Pages API integration
 * 
 * This script demonstrates how to use the new Cloudflare Pages API endpoints
 * to add and manage custom domains for Pages projects.
 * 
 * Usage:
 * 1. Start your development server: npm run dev
 * 2. Run this script: node test-pages-api.js
 */

const BASE_URL = 'http://localhost:4321';

async function testPagesAPI() {
  console.log('🧪 Testing Cloudflare Pages API Integration\n');

  try {
    // Step 1: Add a domain to the database
    console.log('1️⃣ Adding domain to database...');
    const addDomainResponse = await fetch(`${BASE_URL}/api/custom-domains`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        domain: 'test-example.com',
        pages_project_name: 'my-test-project',
        user_id: 'test-user'
      })
    });

    const addDomainResult = await addDomainResponse.json();
    console.log('✅ Domain added:', addDomainResult);

    if (!addDomainResult.success) {
      console.error('❌ Failed to add domain');
      return;
    }

    const domainId = addDomainResult.domain.id;
    console.log(`📝 Domain ID: ${domainId}\n`);

    // Step 2: Configure domain with Cloudflare Pages
    console.log('2️⃣ Configuring domain with Cloudflare Pages...');
    const setupResponse = await fetch(`${BASE_URL}/api/custom-domains/cloudflare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        domain_id: domainId,
        action: 'setup',
        user_id: 'test-user'
      })
    });

    const setupResult = await setupResponse.json();
    console.log('✅ Cloudflare setup result:', setupResult);

    if (!setupResult.success) {
      console.log('⚠️ Cloudflare setup failed (this is expected if API credentials are not configured)');
      console.log('Error:', setupResult.error);
    } else {
      console.log('🎉 Domain successfully configured with Cloudflare Pages!');
      console.log('Pages Domain ID:', setupResult.pages_domain_id);
      console.log('Zone ID:', setupResult.zone_id);
    }

    // Step 3: Check domain status
    console.log('\n3️⃣ Checking domain status...');
    const statusResponse = await fetch(
      `${BASE_URL}/api/custom-domains/cloudflare?domain_id=${domainId}&user_id=test-user`
    );

    const statusResult = await statusResponse.json();
    console.log('✅ Domain status:', statusResult);

    // Step 4: List all domains
    console.log('\n4️⃣ Listing all domains...');
    const listResponse = await fetch(`${BASE_URL}/api/custom-domains?user_id=test-user`);
    const listResult = await listResponse.json();
    console.log('✅ All domains:', listResult);

    // Step 5: Clean up - remove domain from Cloudflare (optional)
    console.log('\n5️⃣ Cleaning up - removing domain from Cloudflare...');
    const removeResponse = await fetch(`${BASE_URL}/api/custom-domains/cloudflare`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        domain_id: domainId,
        action: 'remove',
        user_id: 'test-user'
      })
    });

    const removeResult = await removeResponse.json();
    console.log('✅ Remove result:', removeResult);

    // Step 6: Delete domain from database
    console.log('\n6️⃣ Deleting domain from database...');
    const deleteResponse = await fetch(
      `${BASE_URL}/api/custom-domains?id=${domainId}&user_id=test-user`,
      { method: 'DELETE' }
    );

    const deleteResult = await deleteResponse.json();
    console.log('✅ Delete result:', deleteResult);

    console.log('\n🎉 Test completed successfully!');

  } catch (error) {
    console.error('❌ Test failed:', error);
  }
}

// Helper function to test just the API methods without Cloudflare
async function testDatabaseOnly() {
  console.log('🧪 Testing Database-Only Operations\n');

  try {
    // Add domain
    const addResponse = await fetch(`${BASE_URL}/api/custom-domains`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        domain: 'database-test.com',
        pages_project_name: 'test-project',
        user_id: 'test-user'
      })
    });

    const addResult = await addResponse.json();
    console.log('✅ Added domain:', addResult.domain?.domain);

    // List domains
    const listResponse = await fetch(`${BASE_URL}/api/custom-domains?user_id=test-user`);
    const listResult = await listResponse.json();
    console.log('✅ Total domains:', listResult.domains?.length);

    // Clean up
    if (addResult.domain?.id) {
      await fetch(`${BASE_URL}/api/custom-domains?id=${addResult.domain.id}&user_id=test-user`, {
        method: 'DELETE'
      });
      console.log('✅ Cleaned up test domain');
    }

  } catch (error) {
    console.error('❌ Database test failed:', error);
  }
}

// Run the appropriate test based on command line argument
const testType = process.argv[2] || 'full';

if (testType === 'db') {
  testDatabaseOnly();
} else {
  testPagesAPI();
}

console.log('\n📚 Usage:');
console.log('  node test-pages-api.js       # Full test with Cloudflare API');
console.log('  node test-pages-api.js db    # Database-only test');
console.log('\n💡 Make sure your development server is running on http://localhost:4321');
