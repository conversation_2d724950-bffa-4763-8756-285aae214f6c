# Cloudflare Pages API Integration

This document explains how to use the new Cloudflare Pages API integration for adding custom domains to Pages projects.

## Overview

The integration now uses the official Cloudflare Pages API endpoint to add domains to Pages projects:
- **Endpoint**: `POST /accounts/{account_id}/pages/projects/{project_name}/domains`
- **Documentation**: https://developers.cloudflare.com/api/operations/pages-project-add-domain

## Setup

### 1. Environment Variables

Set the following secrets in your Cloudflare Workers environment:

```bash
# Set via Wrangler CLI
wrangler secret put CLOUDFLARE_API_TOKEN
wrangler secret put CLOUDFLARE_ACCOUNT_ID
```

**CLOUDFLARE_API_TOKEN**: Your Cloudflare API token with Pages Write permissions
**CLOUDFLARE_ACCOUNT_ID**: Your Cloudflare account ID

### 2. API Token Permissions

Your Cloudflare API token must have the following permissions:
- **Pages Write** (required for adding/removing domains from Pages projects)
- **Zone Read** (optional, for DNS record management)
- **DNS Write** (optional, for automatic DNS record creation)

## Usage

### Adding a Domain to a Pages Project

1. **First, add the domain to your database** (optional Pages project name):
```bash
POST /api/custom-domains
Content-Type: application/json

{
  "domain": "example.com",
  "pages_project_name": "my-pages-project",
  "user_id": "default-user"
}
```

2. **Then, configure it with Cloudflare Pages**:
```bash
POST /api/custom-domains/cloudflare
Content-Type: application/json

{
  "domain_id": "domain-uuid-from-step-1",
  "action": "setup",
  "user_id": "default-user"
}
```

### Removing a Domain from a Pages Project

```bash
POST /api/custom-domains/cloudflare
Content-Type: application/json

{
  "domain_id": "domain-uuid",
  "action": "remove",
  "user_id": "default-user"
}
```

## API Methods

### CloudflareAPI.addDomainToPages(domain, projectName)

Adds a domain to a Cloudflare Pages project using the official API.

**Parameters:**
- `domain` (string): The domain name to add (e.g., "example.com")
- `projectName` (string): The Pages project name

**Returns:**
- Cloudflare API response with domain details including ID, certificate authority, creation date, etc.

### CloudflareAPI.removeDomainFromPages(domain, projectName)

Removes a domain from a Cloudflare Pages project.

**Parameters:**
- `domain` (string): The domain name to remove
- `projectName` (string): The Pages project name

**Returns:**
- Success confirmation

### CloudflareAPI.setupDomainForPages(domain, projectName)

Legacy method that now combines the official Pages API with DNS record management.

**What it does:**
1. Calls `addDomainToPages()` to add the domain to the Pages project
2. Creates/updates DNS CNAME record pointing to `{projectName}.pages.dev`
3. Enables Cloudflare proxy for the domain

## Response Format

### Successful Domain Addition

```json
{
  "success": true,
  "message": "Domain configured successfully with Cloudflare Pages",
  "zone_id": "zone-id-if-dns-configured",
  "pages_domain_id": "pages-domain-id",
  "pages_result": {
    "id": "domain-id-in-pages",
    "certificate_authority": "lets_encrypt",
    "created_on": "2025-01-31T10:00:00Z",
    "name": "example.com",
    "status": "pending",
    "verification_method": "http"
  }
}
```

### Error Response

```json
{
  "success": false,
  "error": "Error message",
  "details": "Detailed error information"
}
```

## Database Schema

The `custom_domains` table stores the following Cloudflare-related fields:

- `cloudflare_zone_id`: Zone ID if domain is managed by Cloudflare DNS
- `cloudflare_custom_hostname_id`: Domain ID from the Pages project
- `pages_project_name`: Name of the associated Pages project

## Troubleshooting

### Common Issues

1. **"Pages project name is required"**: Ensure the domain has a `pages_project_name` set in the database
2. **"Cloudflare API not configured"**: Set the required environment variables
3. **"Domain not found in Pages project"**: The domain may not be added to the project yet

### Checking Domain Status

```bash
GET /api/custom-domains/cloudflare?domain_id=domain-uuid&user_id=default-user
```

This endpoint returns the current Cloudflare configuration status for a domain.

## Migration from Legacy Method

If you were using the old DNS-only approach, the new integration:
- ✅ Uses official Cloudflare Pages API
- ✅ Properly registers domains with Pages projects
- ✅ Still manages DNS records automatically
- ✅ Provides better error handling and status tracking

No changes needed to existing domain records - they will work with both old and new methods.
