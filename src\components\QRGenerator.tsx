import React, { useEffect, useRef, useState, useCallback } from "react";
import QRCodeStyling from "qr-code-styling";
import { Download, Check, Link2, MessageCircle, Upload, AlertCircle, CheckCircle, ArrowLeft, ArrowRight, LogIn, QrCode, BarChart2, Globe, User, Mail, Wifi, XCircle } from "lucide-react";
import { v4 as uuidv4 } from "uuid";
import { useQRGeneratorStore } from "../stores/qrGeneratorStore";
import { useAuthStore } from "../stores/authStore";
import { validateQROptions } from "../constants/qrDefaults";

// Shadcn UI components
import { Button } from "./ui/button";
import { Input } from "./ui/input";
import { Label } from "./ui/label";
import { Textarea } from "./ui/textarea";
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "./ui/card";
import { Progress } from "./ui/progress";
import { Badge } from "./ui/badge";
import { <PERSON><PERSON>, AlertDescription } from "./ui/alert";
import { Tabs, TabsContent, TabsList, TabsTrigger } from "./ui/tabs";
import { Separator } from "./ui/separator";
import { Switch } from "./ui/switch";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select";

// ---------------------------------------------
// 1. Types and Interfaces
// ---------------------------------------------

interface User {
  email: string;
  name: string;
  id?: string;
}

interface CustomDomain {
  domain: string;
  verified: boolean;
  id: string;
}

interface Session {
  user: User;
}

// ---------------------------------------------
// 2. Design-tokens & constants
// ---------------------------------------------

const colorSchemes = [
  { name: "Classic", primary: "#000000", secondary: "#000000" },
  { name: "Ocean", primary: "#0369A1", secondary: "#0284C7" },
  { name: "Forest", primary: "#166534", secondary: "#15803D" },
  { name: "Royal", primary: "#3730A3", secondary: "#4338CA" },
  { name: "Sunset", primary: "#9D174D", secondary: "#BE185D" },
  { name: "Elegant", primary: "#18181B", secondary: "#27272A" },
];

const qrStyles = {
  dots: [
    { id: "square", label: "Square" },
    { id: "rounded", label: "Rounded" },
    { id: "dots", label: "Dots" },
    { id: "classy", label: "Classy" },
    { id: "classy-rounded", label: "Classy-Round" },
    { id: "extra-rounded", label: "Extra-Round" },
  ],
  cornerSquare: [
    { id: "square", label: "Square" },
    { id: "extra-rounded", label: "Rounded" },
    { id: "dot", label: "Dot" },
  ],
  cornerDot: [
    { id: "square", label: "Square" },
    { id: "dot", label: "Dot" },
  ],
};

// Default QR options
const defaultOptions: any = {
  width: 300,
  height: 300,
  data: "",
  image: "",
  margin: 20,
  dotsOptions: {
    color: "#000000",
    type: "square",
  },
  backgroundOptions: {
    color: "#ffffff",
  },
  imageOptions: {
    hideBackgroundDots: true,
    imageSize: 0.3,
    margin: 10,
    crossOrigin: "anonymous",
  },
  cornersSquareOptions: {
    type: "square",
    color: "#000000",
  },
  cornersDotOptions: {
    type: "dot",
    color: "#000000",
  },
  qrOptions: {
    errorCorrectionLevel: "H",
  },
};

// ---------------------------------------------
// 3. Mock functions (replace with actual implementation)
// ---------------------------------------------

// ---------------------------------------------
// 3.1  (Deprecated) local auth logic removed. Global auth handled via Zustand store
// ---------------------------------------------

// ---------------------------------------------
// 4. Component
// ---------------------------------------------

const QRGenerator: React.FC = () => {
  // Auth state (global)
  const { session, status, init, signIn } = useAuthStore();
  console.log(session, "session")

  // Initialize authentication status on first mount
  useEffect(() => {
    console.log("Debug: Initializing auth...");
    init();

    // Add a small delay and check again
    setTimeout(() => {
      console.log("Debug: After timeout - session:", session, "status:", status);
    }, 1000);
  }, [init]);

  // Global QR generator state (managed by Zustand)
  const {
    step,
    setStep,
    contentType,
    setContentType,
    formData,
    setFormData,
    customUrl,
    setCustomUrl,
    urlAvailability,
    setUrlAvailability,
    options,
    setOptions,
    isDynamicQR,
    setIsDynamicQR,
    qrCodeName,
    setQrCodeName,
    selectedDomain,
    setSelectedDomain,
    customDomains,
    setCustomDomains,
    errors,
    setErrors,
  } = useQRGeneratorStore();

  // QR-code instance (local)
  const [qrCode, setQrCode] = useState<any>(null);
  const canvasRef = useRef<HTMLDivElement>(null);
  // Local ref for logo file upload input
  const fileInputRef = useRef<HTMLInputElement>(null);

  // Download progress state
  const [downloadProgress, setDownloadProgress] = useState({
    isDownloading: false,
    step: "",
    progress: 0
  });

  // Upload status state
  const [uploadStatus, setUploadStatus] = useState({
    isUploading: false,
    error: null as string | null,
    success: false
  });

  // Download format state
  const [downloadFormat, setDownloadFormat] = useState<"png" | "jpeg" | "svg">("png");

  //--------------------------------------------------
  // 4.1  Fetch user's custom domains
  //--------------------------------------------------
  useEffect(() => {
    const fetchCustomDomains = async () => {
      if (!session?.user?.email) return;

      try {
        const response = await fetch("/api/get-qr-list", {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            type: "customdomainsettings",
            email: session.user.email,
          }),
        });

        const data: any = await response.json();
        if (data.result && data.message.length > 0) {
          const domains = data.message.map((item: any) => ({
            domain: item.domain,
            verified: item.verified === 1,
            id: item.id,
          }));
          setCustomDomains(domains);
        }
      } catch (error) {
        console.error("Error fetching custom domains:", error);
      }
    };

    fetchCustomDomains();
  }, [session?.user?.email]);

  //--------------------------------------------------
  // 4.2  Initialise QRCodeStyling once after mount
  //--------------------------------------------------
  useEffect(() => {
    if (!qrCode) {
      try {
        const validatedOptions = validateQROptions(options);
        const instance = new QRCodeStyling(validatedOptions);
        setQrCode(instance);
      } catch (error) {
        console.error("Error initializing QR code:", error);
        // Fallback to default options if there's an error
        const fallbackOptions = validateQROptions(defaultOptions);
        const instance = new QRCodeStyling(fallbackOptions);
        setQrCode(instance);
      }
    }
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, []);

  //--------------------------------------------------
  // 4.3  Update QR code whenever options change
  //--------------------------------------------------
  useEffect(() => {
    if (!qrCode) return;

    try {
      const validatedOptions = validateQROptions(options);
      qrCode.update(validatedOptions);
      if (canvasRef.current) {
        canvasRef.current.innerHTML = ""; // clear previous
        qrCode.append(canvasRef.current);
      }
    } catch (error) {
      console.error("Error updating QR code:", error);
      // Fallback to default options if there's an error
      const fallbackOptions = validateQROptions(defaultOptions);
      qrCode.update(fallbackOptions);
      if (canvasRef.current) {
        canvasRef.current.innerHTML = "";
        qrCode.append(canvasRef.current);
      }
    }
  }, [qrCode, options]);

  //--------------------------------------------------
  // 4.4  Handle data changes (URL / WhatsApp / Email / WiFi)
  //--------------------------------------------------
  useEffect(() => {
    const handleContentChange = () => {
      let data = "";

      if (contentType === "url") {
        data = formData.url;
      } else if (contentType === "whatsapp") {
        const { countryCode, phoneNumber, message } = formData.whatsapp;
        if (countryCode && phoneNumber) {
          data = `https://wa.me/${countryCode}${phoneNumber}?text=${encodeURIComponent(message)}`;
        }
      } else if (contentType === "email") {
        const { email, subject, body } = formData.email;
        if (email) {
          data = `mailto:${email}`;
          const params = [];
          if (subject) params.push(`subject=${encodeURIComponent(subject)}`);
          if (body) params.push(`body=${encodeURIComponent(body)}`);
          if (params.length > 0) {
            data += `?${params.join('&')}`;
          }
        }
      } else if (contentType === "wifi") {
        const { ssid, password, security, hidden } = formData.wifi;
        if (ssid) {
          // WiFi QR code format: WIFI:T:WPA;S:mynetwork;P:mypass;H:false;;
          data = `WIFI:T:${security};S:${ssid};P:${password || ''};H:${hidden ? 'true' : 'false'};;`;
        }
      }

      // Update QR options with new data
      setOptions((prev: any) => ({ ...prev, data }));
    };

    handleContentChange();
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentType, formData]);

  // Separate effect for validation when content type changes
  useEffect(() => {
    // Clear all errors first
    setErrors({});

    // Validate the current content type data after a short delay
    const validateTimeout = setTimeout(() => {
      validateCurrentContentType();
    }, 100);

    return () => clearTimeout(validateTimeout);
    // eslint-disable-next-line react-hooks/exhaustive-deps
  }, [contentType]);

  // Function to validate only the current content type
  const validateCurrentContentType = () => {
    const newErrors: Record<string, any> = {};

    switch (contentType) {
      case "url":
        if (formData.url) {
          const urlError = validateURL(formData.url);
          if (urlError) newErrors.url = urlError;
        }
        break;

      case "whatsapp":
        const whatsappErrors = validateWhatsApp(formData.whatsapp);
        if (Object.keys(whatsappErrors).length > 0) {
          newErrors.whatsapp = whatsappErrors;
        }
        break;

      case "email":
        if (formData.email.email) {
          const emailErrors = validateEmail(formData.email);
          if (Object.keys(emailErrors).length > 0) {
            newErrors.email = emailErrors;
          }
        }
        break;

      case "wifi":
        if (formData.wifi.ssid) {
          const wifiErrors = validateWiFi(formData.wifi);
          if (Object.keys(wifiErrors).length > 0) {
            newErrors.wifi = wifiErrors;
          }
        }
        break;
    }

    setErrors(newErrors);
  };

  //--------------------------------------------------
  // 4.5  Helper functions and validations
  //--------------------------------------------------
  const getContentName = () => {
    switch (contentType) {
      case "url":
        return formData.url;
      case "whatsapp":
        return `WhatsApp ${formData.whatsapp.phoneNumber}`;
      case "email":
        return `Email ${formData.email.email}`;
      case "wifi":
        return `WiFi ${formData.wifi.ssid}`;
      default:
        return "";
    }
  };

  // Validation functions
  const validateURL = (url: string): string | null => {
    // Trim whitespace first
    const trimmed = url.trim();

    // Empty check
    if (!trimmed) return "URL is required";

    // Must start with http:// or https://
    if (!trimmed.startsWith("http://") && !trimmed.startsWith("https://")) {
      return "URL must start with http:// or https://";
    }

    // Use the built-in URL constructor for validation. This is more reliable than the previous
    // overly-restrictive regex because it supports sub-domains, ports, query params, hashes, etc.
    try {
      // The constructor will throw if the string is not a valid URL.
      new URL(trimmed);
    } catch {
      return "Please enter a valid URL";
    }

    // If we reach here, the URL is considered valid.
    return null;
  };

  const validateWhatsApp = (data: typeof formData.whatsapp): Record<string, string> => {
    const errors: Record<string, string> = {};

    // Country code validation
    if (!data.countryCode.trim()) {
      errors.countryCode = "Country code is required";
    } else if (!/^\d{1,4}$/.test(data.countryCode)) {
      errors.countryCode = "Country code must be 1-4 digits";
    }

    // Phone number validation
    if (!data.phoneNumber.trim()) {
      errors.phoneNumber = "Phone number is required";
    } else if (!/^\d{6,15}$/.test(data.phoneNumber.replace(/\s/g, ''))) {
      errors.phoneNumber = "Phone number must be 6-15 digits";
    }

    // Message length validation (optional but limited)
    if (data.message && data.message.length > 1000) {
      errors.message = "Message cannot exceed 1000 characters";
    }

    return errors;
  };

  const validateEmail = (data: typeof formData.email): Record<string, string> => {
    const errors: Record<string, string> = {};

    // Email validation
    if (!data.email.trim()) {
      errors.email = "Email address is required";
    } else {
      const emailPattern = /^[^\s@]+@[^\s@]+\.[^\s@]+$/;
      if (!emailPattern.test(data.email)) {
        errors.email = "Please enter a valid email address";
      } else if (data.email.length > 254) {
        errors.email = "Email address is too long";
      }
    }

    // Subject validation (optional but limited)
    if (data.subject && data.subject.length > 200) {
      errors.subject = "Subject cannot exceed 200 characters";
    }

    // Body validation (optional but limited)
    if (data.body && data.body.length > 2000) {
      errors.body = "Message cannot exceed 2000 characters";
    }

    return errors;
  };

  const validateWiFi = (data: typeof formData.wifi): Record<string, string> => {
    const errors: Record<string, string> = {};

    // SSID validation
    if (!data.ssid.trim()) {
      errors.ssid = "Network name (SSID) is required";
    } else if (data.ssid.length > 32) {
      errors.ssid = "Network name cannot exceed 32 characters";
    } else if (data.ssid.length < 1) {
      errors.ssid = "Network name must be at least 1 character";
    }

    // Password validation based on security type
    if (data.security !== "nopass") {
      if (!data.password.trim()) {
        errors.password = "Password is required for secured networks";
      } else {
        if (data.security === "WEP") {
          // WEP key validation (5, 13, 16, or 29 characters, or 10, 26, 32, or 58 hex chars)
          const isValidWEP = [5, 13, 16, 29].includes(data.password.length) ||
            ([10, 26, 32, 58].includes(data.password.length) && /^[0-9A-Fa-f]+$/.test(data.password));
          if (!isValidWEP) {
            errors.password = "WEP password must be 5, 13, 16, or 29 characters, or 10, 26, 32, or 58 hex characters";
          }
        } else if (data.security === "WPA") {
          // WPA/WPA2 password validation (8-63 characters)
          if (data.password.length < 8 || data.password.length > 63) {
            errors.password = "WPA password must be between 8 and 63 characters";
          }
        }
      }
    }

    return errors;
  };

  const validateQRName = (name: string): string | null => {
    if (name && name.length > 100) {
      return "QR code name cannot exceed 100 characters";
    }

    // Check for invalid characters
    const invalidChars = /[<>:"/\\|?*]/;
    if (name && invalidChars.test(name)) {
      return "QR code name contains invalid characters";
    }

    return null;
  };

  const validateCustomSlug = (slug: string): string | null => {
    if (!slug.trim()) return null; // Optional field

    if (slug.length < 3) {
      return "Custom URL must be at least 3 characters";
    }

    if (slug.length > 50) {
      return "Custom URL cannot exceed 50 characters";
    }

    // Only allow letters, numbers, and hyphens
    if (!/^[a-zA-Z0-9-]+$/.test(slug)) {
      return "Custom URL can only contain letters, numbers, and hyphens";
    }

    // Cannot start or end with hyphen
    if (slug.startsWith('-') || slug.endsWith('-')) {
      return "Custom URL cannot start or end with a hyphen";
    }

    // Cannot have consecutive hyphens
    if (slug.includes('--')) {
      return "Custom URL cannot have consecutive hyphens";
    }

    return null;
  };

  // Comprehensive validation for current step
  const validateCurrentStep = (): boolean => {
    const newErrors: Record<string, any> = {};

    if (step === 1) {
      // Validate content based on type
      switch (contentType) {
        case "url":
          const urlError = validateURL(formData.url);
          if (urlError) newErrors.url = urlError;
          break;

        case "whatsapp":
          const whatsappErrors = validateWhatsApp(formData.whatsapp);
          if (Object.keys(whatsappErrors).length > 0) {
            newErrors.whatsapp = whatsappErrors;
          }
          break;

        case "email":
          const emailErrors = validateEmail(formData.email);
          if (Object.keys(emailErrors).length > 0) {
            newErrors.email = emailErrors;
          }
          break;

        case "wifi":
          const wifiErrors = validateWiFi(formData.wifi);
          if (Object.keys(wifiErrors).length > 0) {
            newErrors.wifi = wifiErrors;
          }
          break;
      }

      // Validate QR name
      const nameError = validateQRName(qrCodeName);
      if (nameError) newErrors.qrName = nameError;
    }

    if (step === 3 && isDynamicQR) {
      // Validate custom slug
      const slugError = validateCustomSlug(customUrl);
      if (slugError) newErrors.customSlug = slugError;
    }

    setErrors(newErrors);
    return Object.keys(newErrors).length === 0;
  };

  // Real-time validation for individual fields
  const validateField = (field: string, value: any) => {
    const newErrors = { ...errors };

    switch (field) {
      case "url":
        const urlError = validateURL(value);
        if (urlError) {
          newErrors.url = urlError;
        } else {
          delete newErrors.url;
        }
        break;

      case "qrName":
        const nameError = validateQRName(value);
        if (nameError) {
          newErrors.qrName = nameError;
        } else {
          delete newErrors.qrName;
        }
        break;

      case "customSlug":
        const slugError = validateCustomSlug(value);
        if (slugError) {
          newErrors.customSlug = slugError;
        } else {
          delete newErrors.customSlug;
        }
        break;

      case "whatsapp":
        const whatsappErrors = validateWhatsApp(formData.whatsapp);
        if (Object.keys(whatsappErrors).length > 0) {
          newErrors.whatsapp = whatsappErrors;
        } else {
          delete newErrors.whatsapp;
        }
        break;

      case "email":
        const emailErrors = validateEmail(formData.email);
        if (Object.keys(emailErrors).length > 0) {
          newErrors.email = emailErrors;
        } else {
          delete newErrors.email;
        }
        break;

      case "wifi":
        const wifiErrors = validateWiFi(formData.wifi);
        if (Object.keys(wifiErrors).length > 0) {
          newErrors.wifi = wifiErrors;
        } else {
          delete newErrors.wifi;
        }
        break;
    }

    setErrors(newErrors);
  };

  //--------------------------------------------------
  // 4.6  Actions (apply scheme, download, upload logo)
  //--------------------------------------------------
  const applyColorScheme = (scheme: { primary: string; secondary: string }) => {
    setOptions((prev: any) => {
      const newOptions = {
        ...prev,
        dotsOptions: { ...prev.dotsOptions, color: scheme.primary },
        cornersSquareOptions: { ...prev.cornersSquareOptions, color: scheme.secondary },
      };
      return validateQROptions(newOptions);
    });
  };

  const handleLogoUpload = async (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0];
    if (!file) return;

    // Reset upload status
    setUploadStatus({ isUploading: false, error: null, success: false });

    // Validate file type and size
    if (!file.type.startsWith('image/')) {
      setUploadStatus({ isUploading: false, error: 'Please select a valid image file', success: false });
      console.error('Invalid file type:', file.type);
      return;
    }

    if (file.size > 5 * 1024 * 1024) { // 5MB limit
      setUploadStatus({ isUploading: false, error: 'File size must be less than 5MB', success: false });
      console.error('File too large:', file.size);
      return;
    }

    setUploadStatus({ isUploading: true, error: null, success: false });

    console.log('Processing logo upload:', {
      name: file.name,
      type: file.type,
      size: file.size
    });

    try {
      const base64 = await new Promise<string>((resolve) => {
        const reader = new FileReader();
        reader.onload = (ev) => resolve(ev.target?.result as string);
        reader.readAsDataURL(file);
      });

      console.log('Base64 generated, length:', base64.length);

      setOptions((prev: any) => {
        const newOptions = {
          ...prev,
          image: base64,
        };
        // Validate and ensure the new options don't cause negative values
        return validateQROptions(newOptions);
      });

      setUploadStatus({ isUploading: false, error: null, success: true });
    } catch (error) {
      console.error('Error processing logo:', error);
      setUploadStatus({ isUploading: false, error: 'Failed to process image', success: false });
    }
  };

  const dataURLtoBlob = (dataUrl: string) => {
    const [header, base64] = dataUrl.split(",");
    const mimeMatch = header.match(/data:(.*);base64/);
    const mime = mimeMatch ? mimeMatch[1] : "image/png";
    const binary = atob(base64);
    const array = new Uint8Array(binary.length);
    for (let i = 0; i < binary.length; i++) array[i] = binary.charCodeAt(i);
    return new Blob([array], { type: mime });
  };

  const handleDownload = async () => {
    if (!qrCode) return;

    setDownloadProgress({ isDownloading: true, step: "Starting download...", progress: 10 });

    try {
      let updatedOptions = { ...options };
      let trackingUrl = "";

      // Step 1: Upload logo if needed
      setDownloadProgress({ isDownloading: true, step: "Uploading logo...", progress: 20 });
      if (updatedOptions.image && updatedOptions.image.startsWith("data:")) {
        try {
          console.log("Starting logo upload...");
          const logoBlob = dataURLtoBlob(updatedOptions.image);
          console.log("Logo blob created:", logoBlob.size, logoBlob.type);

          const form = new FormData();
          form.append("file", logoBlob, `logo-${uuidv4()}.png`);

          console.log("Sending upload request...");
          const res = await fetch("/api/upload-logo", { method: "POST", body: form });
          console.log("Upload response status:", res.status);

          if (res.ok) {
            const uploadRes = (await res.json()) as {
              url: string;
              development?: boolean;
              message?: string;
            };
            const url = uploadRes.url;
            console.log("Upload successful, URL:", url);

            if (uploadRes.development) {
              console.log("Development mode:", uploadRes.message);
              // In development, keep the base64 image for preview
              // but log that it would be uploaded in production
            } else {
              // Production mode - use the uploaded URL
              updatedOptions.image = url;
              setOptions((prev: any) => ({ ...prev, image: url }));
            }
          } else {
            const errorText = await res.text();
            console.error("Upload failed with status:", res.status, errorText);
            // Continue without logo if upload fails
            updatedOptions.image = "";
          }
        } catch (err) {
          console.error("Logo upload failed", err);
          // Continue without logo if upload fails
          updatedOptions.image = "";
        }
      }

      // Step 2: Store QR code and generate tracking URL if analytics enabled
      setDownloadProgress({ isDownloading: true, step: "Generating tracking URL...", progress: 40 });
      
      if (isDynamicQR && session?.user) {
        // Only store in database for dynamic QR codes with analytics
        try {
          const storeResponse = await fetch("/api/store-qr", {
            method: "POST",
            headers: { "Content-Type": "application/json" },
            body: JSON.stringify({
              name: qrCodeName || getContentName() || "qr-code",
              options: updatedOptions,
              customUrl: customUrl,
              isDynamic: isDynamicQR,
              domain: selectedDomain || "qr.qranalytica.com",
              userEmail: session.user.email,
              contentType: contentType,
              formData: formData,
            }),
          });

          const storeData: any = await storeResponse.json();
          if (storeResponse.ok && storeData.custom_slug) {
            // Generate tracking URL using the domain and custom slug
            const domain = selectedDomain || "qr.qranalytica.com";
            trackingUrl = `https://${domain}/${storeData.custom_slug}`;
            
            // Update QR options with tracking URL
            updatedOptions.data = trackingUrl;
            setOptions((prev: any) => ({ ...prev, data: trackingUrl }));
          } else {
            console.error("Failed to store QR code:", storeData.error);
          }
        } catch (err) {
          console.error("Error storing QR code:", err);
        }
      } else {
        // Static QR - no database storage needed, just update progress
        setDownloadProgress({ isDownloading: true, step: "Preparing download...", progress: 50 });
      }

      // Step 3: Generate high-resolution QR code
      setDownloadProgress({ isDownloading: true, step: "Generating QR code...", progress: 70 });
      
      // If we have a tracking URL, update the current QR code to show it
      if (trackingUrl && qrCode) {
        const previewOptions = validateQROptions({ ...updatedOptions, data: trackingUrl });
        qrCode.update(previewOptions);
        if (canvasRef.current) {
          canvasRef.current.innerHTML = "";
          qrCode.append(canvasRef.current);
        }
      }

      // Step 4: Download high-res QR code in selected format
      setDownloadProgress({ isDownloading: true, step: "Downloading...", progress: 90 });

      const highResOptions = validateQROptions({ ...updatedOptions, width: 2400, height: 2400 });
      const temp = new QRCodeStyling(highResOptions);
      temp.download({ extension: downloadFormat, name: qrCodeName || "qr-code" });

      // Complete
      setDownloadProgress({ isDownloading: true, step: "Complete!", progress: 100 });
      
      // Reset progress after a short delay
      setTimeout(() => {
        setDownloadProgress({ isDownloading: false, step: "", progress: 0 });
      }, 1500);

    } catch (error) {
      console.error("Download failed:", error);
      setDownloadProgress({ isDownloading: false, step: "", progress: 0 });
    }
  };

  // Debounced URL availability check
  const checkUrlAvailability = useCallback(
    async (slug: string) => {
      if (!slug.trim()) {
        setUrlAvailability({ checking: false, available: null, message: "" });
        return;
      }

      setUrlAvailability({ checking: true, available: null, message: "Checking..." });

      try {
        const response = await fetch(`/api/check-url-availability?slug=${encodeURIComponent(slug)}`);
        const data: any = await response.json();

        if (data.available) {
          setUrlAvailability({
            checking: false,
            available: true,
            message: "✓ Available",
          });
        } else {
          setUrlAvailability({
            checking: false,
            available: false,
            message: "✗ Already taken",
          });
        }
      } catch (err) {
        setUrlAvailability({
          checking: false,
          available: null,
          message: "Error checking availability",
        });
      }
    },
    []
  );

  // Debounce the URL check
  useEffect(() => {
    const timer = setTimeout(() => {
      if (customUrl) {
        checkUrlAvailability(customUrl);
      }
    }, 500);

    return () => clearTimeout(timer);
  }, [customUrl, checkUrlAvailability]);

  //--------------------------------------------------
  // 5.  Render helpers
  //--------------------------------------------------

  const WizardNav = () => {
    const stepLabels = [
      { label: "Content", icon: Link2, desc: "Add your content" },
      { label: "Design", icon: QrCode, desc: "Customize appearance" },
      { label: "Download", icon: Download, desc: "Get your QR code" }
    ];
    const progressValue = (step / 3) * 100;

    return (
      <div className="mb-12">
        <div className="flex justify-between items-start mb-6">
          {stepLabels.map((item, index) => {
            const stepNum = index + 1;
            const isActive = step === stepNum;
            const isCompleted = step > stepNum;
            const IconComponent = item.icon;

            return (
              <div key={stepNum} className="flex flex-col items-center flex-1 relative">
                <div
                  className={`w-12 h-12 rounded-xl flex items-center justify-center text-sm font-semibold transition-all duration-200 ${isCompleted
                    ? "bg-green-500 text-white shadow-lg shadow-green-500/25"
                    : isActive
                      ? "bg-blue-600 text-white shadow-lg shadow-blue-600/25"
                      : "bg-gray-100 text-gray-400 border-2 border-gray-200"
                    }`}
                >
                  {isCompleted ? (
                    <Check className="w-5 h-5" />
                  ) : (
                    <IconComponent className="w-5 h-5" />
                  )}
                </div>
                <div className="text-center mt-3 max-w-20">
                  <span className={`text-sm font-medium block ${isActive ? "text-blue-600" : isCompleted ? "text-green-600" : "text-gray-500"
                    }`}>
                    {item.label}
                  </span>
                  <span className="text-xs text-gray-400 mt-1 block">
                    {item.desc}
                  </span>
                </div>
                {index < stepLabels.length - 1 && (
                  <div className={`absolute top-6 left-full w-full h-0.5 -translate-x-1/2 ${isCompleted ? "bg-green-500" : "bg-gray-200"
                    }`} style={{ width: 'calc(100% - 3rem)' }} />
                )}
              </div>
            );
          })}
        </div>
        <div className="w-full bg-gray-200 rounded-full h-2 overflow-hidden">
          <div
            className="bg-gradient-to-r from-blue-500 to-blue-600 h-2 rounded-full transition-all duration-500 ease-out"
            style={{ width: `${progressValue}%` }}
          />
        </div>
      </div>
    );
  };

  // Loading Screen
  if (status === "loading") {
    return (
      <div className="min-h-screen bg-gradient-to-br from-slate-50 via-blue-50 to-indigo-50 flex items-center justify-center">
        <div className="text-center">
          <div className="w-12 h-12 border-4 border-blue-200 border-t-blue-600 rounded-full animate-spin mx-auto mb-4" />
          <p className="text-gray-600 font-medium">Loading QR Generator...</p>
        </div>
      </div>
    );
  }

  //--------------------------------------------------
  // 6.  JSX
  //--------------------------------------------------

  return (<div className=" mx-auto px-4 sm:px-6 lg:px-8 py-12">
    <div className="bg-white rounded-2xl shadow-xl border border-gray-200 overflow-hidden">
      <div className="flex flex-col xl:flex-row">
        {/* Left panel */}
        <div className="xl:w-2/3 p-8 lg:p-12">
          <div className="mb-8">
            <div className="flex items-center justify-between mb-6">
              <div>
                <h2 className="text-2xl font-semibold text-slate-800">Stop Wasting Money on Blind QR Codes</h2>
                <p className="text-gray-600 mt-1">Create trackable QR codes that provide real ROI data</p>
              </div>
              {session?.user ? (
                <div className="flex items-center gap-3 bg-teal-50 px-4 py-2 rounded-full border border-teal-200">
                  <div className="w-2 h-2 bg-teal-500 rounded-full"></div>
                  <span className="text-sm font-medium text-teal-700">
                    {session.user.name}
                  </span>
                </div>
              ) : (
                <div className="flex items-center gap-3 bg-red-50 px-4 py-2 rounded-full border border-red-200">
                  <AlertCircle className="w-4 h-4 text-red-600" />
                  <span className="text-sm font-medium text-red-700">
                    No Analytics Without Account
                  </span>
                </div>
              )}
            </div>
          </div>

          <WizardNav />

          <div className="space-y-8">
            {/* Step 1 - Content */}
            {step === 1 && (
              <div className="space-y-8">
                <div className="bg-gradient-to-r from-slate-50 to-slate-100 rounded-xl p-6 border border-slate-200">
                  <div className="flex items-center gap-3 mb-4">
                    <div className="w-8 h-8 bg-red-100 rounded-full flex items-center justify-center">
                      <AlertCircle className="w-4 h-4 text-red-600" />
                    </div>
                    <div>
                       <p className="text-gray-600 text-sm">Choose your content type and get real tracking data</p>
                    </div>
                  </div>

                  {!session?.user && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-4 mb-6">
                      <div className="flex items-center gap-2 mb-2">
                        <AlertCircle className="w-4 h-4 text-red-600" />
                        <span className="text-sm font-medium text-red-800">Warning: No Analytics Without Account</span>
                      </div>
                      <p className="text-red-700 text-sm">
                        Without an account, you're creating blind QR codes with zero tracking. You'll have no idea if your campaigns work.
                      </p>
                    </div>
                  )}
                  <Tabs value={contentType} onValueChange={(value) => setContentType(value as "url" | "whatsapp" | "email" | "wifi")} className="w-full">
                    <TabsList className="grid w-full grid-cols-2 h-12 bg-white border shadow-sm">
                      <TabsTrigger value="url" className="flex items-center gap-2 data-[state=active]:bg-slate-800 data-[state=active]:text-white">
                        <Link2 className="w-4 h-4" />
                        Website
                      </TabsTrigger>
                      <TabsTrigger value="whatsapp" className="flex items-center gap-2 data-[state=active]:bg-teal-600 data-[state=active]:text-white">
                        <MessageCircle className="w-4 h-4" />
                        WhatsApp
                      </TabsTrigger>
                      {/* <TabsTrigger value="email" className="flex items-center gap-2 data-[state=active]:bg-slate-700 data-[state=active]:text-white">
                        <Mail className="w-4 h-4" />
                        Email
                      </TabsTrigger>
                      <TabsTrigger value="wifi" className="flex items-center gap-2 data-[state=active]:bg-teal-700 data-[state=active]:text-white">
                        <Wifi className="w-4 h-4" />
                        WiFi
                      </TabsTrigger> */}
                    </TabsList>

                    <TabsContent value="url" className="mt-4">
                      <div className="space-y-2">
                        <Label htmlFor="url">Enter URL *</Label>
                        <Input
                          id="url"
                          type="url"
                          value={formData.url}
                          onChange={(e) => {
                            setFormData({ ...formData, url: e.target.value });
                            validateField("url", e.target.value);
                          }}
                          onBlur={(e) => validateField("url", e.target.value)}
                          placeholder="https://example.com"
                          className={errors.url ? "border-destructive focus-visible:ring-destructive" : ""}
                        />
                        {errors.url && (
                          <Alert variant="destructive">
                            <AlertCircle className="h-4 w-4" />
                            <AlertDescription>{errors.url}</AlertDescription>
                          </Alert>
                        )}
                        <p className="text-xs text-muted-foreground">
                          Enter a complete URL starting with http:// or https://
                        </p>
                      </div>
                    </TabsContent>

                    <TabsContent value="whatsapp" className="mt-4">
                      <div className="space-y-4">
                        <div className="grid grid-cols-3 gap-4">
                          <div className="space-y-2">
                            <Label htmlFor="countryCode">Country Code *</Label>
                            <Input
                              id="countryCode"
                              type="text"
                              value={formData.whatsapp.countryCode}
                              onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 4);
                                setFormData({
                                  ...formData,
                                  whatsapp: { ...formData.whatsapp, countryCode: value },
                                });
                              }}
                              onBlur={() => validateField("whatsapp", formData.whatsapp)}
                              placeholder="971"
                              className={errors.whatsapp?.countryCode ? "border-destructive focus-visible:ring-destructive" : ""}
                            />
                            {errors.whatsapp?.countryCode && (
                              <p className="text-xs text-destructive">{errors.whatsapp.countryCode}</p>
                            )}
                          </div>
                          <div className="col-span-2 space-y-2">
                            <Label htmlFor="phoneNumber">Phone Number *</Label>
                            <Input
                              id="phoneNumber"
                              type="tel"
                              value={formData.whatsapp.phoneNumber}
                              onChange={(e) => {
                                const value = e.target.value.replace(/\D/g, '').slice(0, 15);
                                setFormData({
                                  ...formData,
                                  whatsapp: { ...formData.whatsapp, phoneNumber: value },
                                });
                              }}
                              onBlur={() => validateField("whatsapp", formData.whatsapp)}
                              placeholder="555123456"
                              className={errors.whatsapp?.phoneNumber ? "border-destructive focus-visible:ring-destructive" : ""}
                            />
                            {errors.whatsapp?.phoneNumber && (
                              <p className="text-xs text-destructive">{errors.whatsapp.phoneNumber}</p>
                            )}
                          </div>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="message">Message (Optional)</Label>
                          <Textarea
                            id="message"
                            rows={4}
                            value={formData.whatsapp.message}
                            onChange={(e) => {
                              const value = e.target.value.slice(0, 1000);
                              setFormData({
                                ...formData,
                                whatsapp: { ...formData.whatsapp, message: value },
                              });
                            }}
                            placeholder="Hello! I'd like to get in touch..."
                            className={`resize-none ${errors.whatsapp?.message ? "border-destructive focus-visible:ring-destructive" : ""}`}
                          />
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-muted-foreground">
                              Pre-fill the WhatsApp message (optional)
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {formData.whatsapp.message.length}/1000
                            </span>
                          </div>
                          {errors.whatsapp?.message && (
                            <p className="text-xs text-destructive">{errors.whatsapp.message}</p>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="email" className="mt-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="email">Email Address *</Label>
                          <Input
                            id="email"
                            type="email"
                            value={formData.email.email}
                            onChange={(e) => {
                              const value = e.target.value.slice(0, 254);
                              setFormData({
                                ...formData,
                                email: { ...formData.email, email: value },
                              });
                            }}
                            onBlur={() => validateField("email", formData.email)}
                            placeholder="<EMAIL>"
                            className={errors.email?.email ? "border-destructive focus-visible:ring-destructive" : ""}
                          />
                          {errors.email?.email && (
                            <p className="text-xs text-destructive">{errors.email.email}</p>
                          )}
                          <p className="text-xs text-muted-foreground">
                            The recipient's email address
                          </p>
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="subject">Subject (Optional)</Label>
                          <Input
                            id="subject"
                            type="text"
                            value={formData.email.subject}
                            onChange={(e) => {
                              const value = e.target.value.slice(0, 200);
                              setFormData({
                                ...formData,
                                email: { ...formData.email, subject: value },
                              });
                            }}
                            placeholder="Inquiry about your services"
                            className={errors.email?.subject ? "border-destructive focus-visible:ring-destructive" : ""}
                          />
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-muted-foreground">
                              Pre-fill the email subject line
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {formData.email.subject.length}/200
                            </span>
                          </div>
                          {errors.email?.subject && (
                            <p className="text-xs text-destructive">{errors.email.subject}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="body">Message (Optional)</Label>
                          <Textarea
                            id="body"
                            rows={4}
                            value={formData.email.body}
                            onChange={(e) => {
                              const value = e.target.value.slice(0, 2000);
                              setFormData({
                                ...formData,
                                email: { ...formData.email, body: value },
                              });
                            }}
                            placeholder="Hello, I would like to..."
                            className={`resize-none ${errors.email?.body ? "border-destructive focus-visible:ring-destructive" : ""}`}
                          />
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-muted-foreground">
                              Pre-fill the email body content
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {formData.email.body.length}/2000
                            </span>
                          </div>
                          {errors.email?.body && (
                            <p className="text-xs text-destructive">{errors.email.body}</p>
                          )}
                        </div>
                      </div>
                    </TabsContent>

                    <TabsContent value="wifi" className="mt-4">
                      <div className="space-y-4">
                        <div className="space-y-2">
                          <Label htmlFor="ssid">Network Name (SSID) *</Label>
                          <Input
                            id="ssid"
                            type="text"
                            value={formData.wifi.ssid}
                            onChange={(e) => {
                              const value = e.target.value.slice(0, 32);
                              setFormData({
                                ...formData,
                                wifi: { ...formData.wifi, ssid: value },
                              });
                            }}
                            onBlur={() => validateField("wifi", formData.wifi)}
                            placeholder="MyWiFiNetwork"
                            className={errors.wifi?.ssid ? "border-destructive focus-visible:ring-destructive" : ""}
                          />
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-muted-foreground">
                              The name of your WiFi network
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {formData.wifi.ssid.length}/32
                            </span>
                          </div>
                          {errors.wifi?.ssid && (
                            <p className="text-xs text-destructive">{errors.wifi.ssid}</p>
                          )}
                        </div>
                        <div className="space-y-2">
                          <Label htmlFor="security">Security Type *</Label>
                          <Select
                            value={formData.wifi.security}
                            onValueChange={(value) => {
                              setFormData({
                                ...formData,
                                wifi: { ...formData.wifi, security: value, password: value === "nopass" ? "" : formData.wifi.password },
                              });
                            }}
                          >
                            <SelectTrigger>
                              <SelectValue placeholder="Select security type" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="WPA">WPA/WPA2 (Recommended)</SelectItem>
                              <SelectItem value="WEP">WEP (Legacy)</SelectItem>
                              <SelectItem value="nopass">None (Open Network)</SelectItem>
                            </SelectContent>
                          </Select>
                          <p className="text-xs text-muted-foreground">
                            Choose the security protocol used by your network
                          </p>
                        </div>
                        {formData.wifi.security !== "nopass" && (
                          <div className="space-y-2">
                            <Label htmlFor="password">
                              Password *
                              {formData.wifi.security === "WPA" && (
                                <span className="text-muted-foreground font-normal"> (8-63 characters)</span>
                              )}
                              {formData.wifi.security === "WEP" && (
                                <span className="text-muted-foreground font-normal"> (WEP key format)</span>
                              )}
                            </Label>
                            <Input
                              id="password"
                              type="password"
                              value={formData.wifi.password}
                              onChange={(e) => {
                                const value = e.target.value.slice(0, 63);
                                setFormData({
                                  ...formData,
                                  wifi: { ...formData.wifi, password: value },
                                });
                              }}
                              onBlur={() => validateField("wifi", formData.wifi)}
                              placeholder={
                                formData.wifi.security === "WPA"
                                  ? "Enter WPA password (8-63 chars)"
                                  : "Enter WEP key"
                              }
                              className={errors.wifi?.password ? "border-destructive focus-visible:ring-destructive" : ""}
                            />
                            {formData.wifi.security === "WPA" && (
                              <p className="text-xs text-muted-foreground">
                                WPA passwords must be 8-63 characters long
                              </p>
                            )}
                            {formData.wifi.security === "WEP" && (
                              <p className="text-xs text-muted-foreground">
                                WEP keys: 5/13/16/29 characters or 10/26/32/58 hex digits
                              </p>
                            )}
                            {errors.wifi?.password && (
                              <p className="text-xs text-destructive">{errors.wifi.password}</p>
                            )}
                          </div>
                        )}
                        <div className="flex items-center space-x-2">
                          <Switch
                            id="hidden"
                            checked={formData.wifi.hidden}
                            onCheckedChange={(checked) =>
                              setFormData({
                                ...formData,
                                wifi: { ...formData.wifi, hidden: checked },
                              })
                            }
                          />
                          <div className="space-y-0.5">
                            <Label htmlFor="hidden">Hidden Network</Label>
                            <p className="text-xs text-muted-foreground">
                              Enable if your network doesn't broadcast its name
                            </p>
                          </div>
                        </div>
                      </div>
                    </TabsContent>
                  </Tabs>
                </div>

                <Separator />

                {/* QR Code Name */}
                <div className="space-y-2">
                  <Label htmlFor="qrName">QR Code Name (Optional)</Label>
                  <Input
                    id="qrName"
                    type="text"
                    value={qrCodeName}
                    onChange={(e) => {
                      const value = e.target.value.slice(0, 100);
                      setQrCodeName(value);
                      validateField("qrName", value);
                    }}
                    onBlur={(e) => validateField("qrName", e.target.value)}
                    placeholder="My QR Code"
                    className={errors.qrName ? "border-destructive focus-visible:ring-destructive" : ""}
                  />
                  <div className="flex justify-between items-center">
                    <p className="text-xs text-muted-foreground">
                      Give your QR code a memorable name for easy identification
                    </p>
                    <span className="text-xs text-muted-foreground">
                      {qrCodeName.length}/100
                    </span>
                  </div>
                  {errors.qrName && (
                    <p className="text-xs text-destructive">{errors.qrName}</p>
                  )}
                </div>

                <Separator />

                {/* Sign in for Analytics (Optional) */}
                <div className="space-y-4">
                  <div>
                    <Label className="text-base font-medium">Analytics & Tracking (Optional)</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Sign in to enable analytics tracking and get insights on your QR code performance
                    </p>
                  </div>

                  {!session?.user ? (
                    <Card className="border-2 border-dashed border-primary/25 bg-primary/5">
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          <div className="flex-shrink-0">
                            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center">
                              <BarChart2 className="w-6 h-6 text-primary" />
                            </div>
                          </div>
                          <div className="flex-1">
                            <h3 className="font-semibold mb-1">Sign in for Analytics (Optional)</h3>
                            <p className="text-sm text-muted-foreground mb-3">
                              Track scans, locations, devices, and get detailed insights on your QR code performance.
                            </p>
                            <Button onClick={signIn} size="sm" className="gap-2">
                              <LogIn className="w-4 h-4" />
                              Sign in with Google
                            </Button>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  ) : (
                    <Card className={`border-${isDynamicQR ? "green" : "red"}-200 bg-${isDynamicQR ? "green" : "red"}-50`}>
                      <CardContent className="p-6">
                        <div className="flex items-center gap-4">
                          <div className="flex-shrink-0">
                            <div className={`w-12 h-12 bg-${isDynamicQR ? "green" : "red"}-100 rounded-full flex items-center justify-center`}>
                              {isDynamicQR ? <CheckCircle className="w-6 h-6 text-green-600" /> : <XCircle className="w-6 h-6 text-red-600" />}
                            </div>
                          </div>
                          <div className="flex-1">
                            {/* <h3 className="font-semibold text-green-800 mb-1">Analytics Enabled</h3> */}
                            <p className={`text-sm text-${isDynamicQR ? "green" : "red"}-700 mb-3`}>
                              Signed in as {session.user.name}. Your QR codes will include analytics tracking.
                            </p>
                            <div className="flex items-center gap-2">
                              <Switch
                                checked={isDynamicQR}
                                onCheckedChange={setIsDynamicQR}
                                id="analytics-toggle"
                              />
                              <Label htmlFor="analytics-toggle" className={`text-sm text-${isDynamicQR ? "green" : "red"}-700`}>
                                Enable detailed analytics tracking
                              </Label>
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )}
                </div>
              </div>
            )}

            {/* Step 2 - Design */}
            {step === 2 && (
              <div className="space-y-8">
                {/* Color schemes */}
                <div>
                  <Label className="text-base font-medium">Color Schemes</Label>
                  <p className="text-sm text-muted-foreground mb-4">Choose a color palette for your QR code</p>
                  <div className="flex flex-wrap gap-4">
                    {colorSchemes.map((scheme) => (
                      <Button
                        key={scheme.name}
                        variant="outline"
                        className="relative aspect-square p-4 h-auto overflow-hidden group hover:scale-105 transition-transform"
                        onClick={() => applyColorScheme(scheme)}
                      >
                        <div
                          className="absolute inset-0"
                          style={{
                            background: `linear-gradient(135deg, ${scheme.primary}, ${scheme.secondary})`,
                          }}
                        />
                        <span className="relative z-10 text-xs text-white font-medium drop-shadow-lg group-hover:scale-110 transition-transform">
                          {scheme.name}
                        </span>
                      </Button>
                    ))}
                  </div>
                </div>

                <Separator />

                {/* Pattern styles */}
                <div className="space-y-6">
                  <div>
                    <Label className="text-base font-medium">Pattern Styles</Label>
                    <p className="text-sm text-muted-foreground">Customize the appearance of your QR code</p>
                  </div>

                  <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Dots Pattern</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {qrStyles.dots.map((style) => (
                          <Button
                            key={style.id}
                            variant={options.dotsOptions.type === style.id ? "default" : "outline"}
                            size="sm"
                            onClick={() =>
                              setOptions((prev: any) => ({
                                ...prev,
                                dotsOptions: { ...prev.dotsOptions, type: style.id },
                              }))
                            }
                            className="justify-start"
                          >
                            {style.label}
                          </Button>
                        ))}
                      </div>
                    </div>

                    <div className="space-y-3">
                      <Label className="text-sm font-medium">Corner Eyes</Label>
                      <div className="grid grid-cols-2 gap-2">
                        {qrStyles.cornerSquare.map((style) => (
                          <Button
                            key={style.id}
                            variant={options.cornersSquareOptions.type === style.id ? "default" : "outline"}
                            size="sm"
                            onClick={() =>
                              setOptions((prev: any) => ({
                                ...prev,
                                cornersSquareOptions: { ...prev.cornersSquareOptions, type: style.id },
                              }))
                            }
                            className="justify-start"
                          >
                            {style.label}
                          </Button>
                        ))}
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}

            {/* Step 3 - Branding & Download */}
            {step === 3 && (
              <div className="space-y-8">
                {/* Logo Upload Section */}
                <div className="space-y-6">
                  <div>
                    <Label className="text-lg font-semibold">Add Your Brand Logo (Optional)</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Make your QR code unique by adding your logo or brand image
                    </p>
                  </div>

                  <Card className="border-2 border-dashed border-muted-foreground/25 hover:border-muted-foreground/50 transition-colors">
                    <CardContent className="p-8">
                      <input
                        ref={fileInputRef}
                        type="file"
                        accept="image/*"
                        className="hidden"
                        onChange={handleLogoUpload}
                      />
                      <Button
                        variant="ghost"
                        className="w-full h-auto flex flex-col items-center gap-4 py-8"
                        onClick={() => fileInputRef.current?.click()}
                        disabled={uploadStatus.isUploading}
                      >
                        {uploadStatus.isUploading ? (
                          <>
                            <div className="w-12 h-12 border-4 border-muted-foreground/25 border-t-muted-foreground rounded-full animate-spin" />
                            <div className="text-center">
                              <div className="font-medium">Processing...</div>
                              <div className="text-sm text-muted-foreground mt-1">
                                Please wait
                              </div>
                            </div>
                          </>
                        ) : (
                          <>
                            <Upload className="w-12 h-12 text-muted-foreground" />
                            <div className="text-center">
                              <div className="font-medium">Upload Logo</div>
                              <div className="text-sm text-muted-foreground mt-1">
                                Click to browse or drag and drop
                              </div>
                            </div>
                          </>
                        )}
                      </Button>
                    </CardContent>
                  </Card>

                  {uploadStatus.error && (
                    <Alert variant="destructive">
                      <AlertCircle className="h-4 w-4" />
                      <AlertDescription>
                        {uploadStatus.error}
                      </AlertDescription>
                    </Alert>
                  )}

                  {options.image && uploadStatus.success && (
                    <Alert>
                      <CheckCircle className="h-4 w-4" />
                      <AlertDescription>
                        Logo uploaded successfully! It will be included in your QR code.
                      </AlertDescription>
                    </Alert>
                  )}
                </div>

                <Separator />

                {/* Analytics Configuration for Signed-in Users */}
                {session?.user && isDynamicQR && (
                  <div className="space-y-6">
                    <div>
                      <Label className="text-lg font-semibold">Analytics Configuration</Label>
                      <p className="text-sm text-muted-foreground mt-1">
                        Configure tracking settings for your dynamic QR code
                      </p>
                    </div>

                    {/* Domain Selection */}
                    <Card>
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          <div className="flex items-center gap-2">
                            <Globe className="w-5 h-5 text-primary" />
                            <Label className="text-base font-medium">Tracking Domain</Label>
                          </div>

                          <Select value={selectedDomain} onValueChange={setSelectedDomain}>
                            <SelectTrigger>
                              <SelectValue placeholder="Select a domain" />
                            </SelectTrigger>
                            <SelectContent>
                              <SelectItem value="qr.qranalytica.com">
                                <div className="flex items-center gap-2">
                                  <span>qr.qranalytica.com</span>
                                  <Badge variant="outline">Default</Badge>
                                </div>
                              </SelectItem>
                              {customDomains.map((domain: CustomDomain) => (
                                <SelectItem key={domain.id} value={domain.domain}>
                                  <div className="flex items-center gap-2">
                                    <span>{domain.domain}</span>
                                    {domain.verified ? (
                                      <Badge variant="default">Verified</Badge>
                                    ) : (
                                      <Badge variant="destructive">Unverified</Badge>
                                    )}
                                  </div>
                                </SelectItem>
                              ))}
                            </SelectContent>
                          </Select>

                          {customDomains.length === 0 && (
                            <p className="text-sm text-muted-foreground">
                              No custom domains found. Using default domain.
                            </p>
                          )}
                        </div>
                      </CardContent>
                    </Card>

                    {/* Custom URL field */}
                    <Card>
                      <CardContent className="p-6">
                        <div className="space-y-4">
                          <Label className="text-base font-medium">Custom URL Slug (Optional)</Label>
                          <div className="flex items-center gap-2">
                            <span className="text-sm text-muted-foreground whitespace-nowrap">
                              {selectedDomain}/
                            </span>
                            <div className="flex-1 relative">
                              <Input
                                type="text"
                                value={customUrl}
                                onChange={(e) => {
                                  const value = e.target.value
                                    .toLowerCase()
                                    .replace(/[^a-z0-9-]/g, '')
                                    .replace(/--+/g, '-')
                                    .slice(0, 50);
                                  setCustomUrl(value);
                                  validateField("customSlug", value);
                                }}
                                onBlur={(e) => validateField("customSlug", e.target.value)}
                                placeholder="my-custom-qr"
                                className={
                                  errors.customSlug
                                    ? "border-destructive focus-visible:ring-destructive pr-10"
                                    : urlAvailability.available === false
                                      ? "border-destructive pr-10"
                                      : urlAvailability.available === true
                                        ? "border-green-500 pr-10"
                                        : "pr-10"
                                }
                              />
                              {customUrl && (
                                <div className="absolute right-3 top-1/2 transform -translate-y-1/2">
                                  {urlAvailability.checking ? (
                                    <div className="w-4 h-4 border-2 border-primary border-t-transparent rounded-full animate-spin" />
                                  ) : errors.customSlug ? (
                                    <AlertCircle className="w-4 h-4 text-destructive" />
                                  ) : urlAvailability.available === true ? (
                                    <CheckCircle className="w-4 h-4 text-green-500" />
                                  ) : urlAvailability.available === false ? (
                                    <AlertCircle className="w-4 h-4 text-destructive" />
                                  ) : null}
                                </div>
                              )}
                            </div>
                            {customUrl && !errors.customSlug && urlAvailability.message && (
                              <Badge
                                variant={
                                  urlAvailability.available === true
                                    ? "default"
                                    : urlAvailability.available === false
                                      ? "destructive"
                                      : "secondary"
                                }
                              >
                                {urlAvailability.message}
                              </Badge>
                            )}
                          </div>
                          <div className="flex justify-between items-center">
                            <p className="text-xs text-muted-foreground">
                              3-50 characters, letters, numbers, and hyphens only
                            </p>
                            <span className="text-xs text-muted-foreground">
                              {customUrl.length}/50
                            </span>
                          </div>
                          {errors.customSlug && (
                            <p className="text-xs text-destructive">{errors.customSlug}</p>
                          )}
                        </div>
                      </CardContent>
                    </Card>
                  </div>
                )}

                {session?.user && isDynamicQR && <Separator />}

                {/* Download Format Selection */}
                <div className="space-y-6">
                  <div>
                    <Label className="text-lg font-semibold">Download Format</Label>
                    <p className="text-sm text-muted-foreground mt-1">
                      Choose the file format for your QR code download
                    </p>
                  </div>

                  <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
                    {/* PNG Option */}
                    <div
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        downloadFormat === "png"
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setDownloadFormat("png")}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          downloadFormat === "png"
                            ? "border-primary bg-primary"
                            : "border-gray-300"
                        }`}>
                          {downloadFormat === "png" && (
                            <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm">PNG</div>
                          <div className="text-xs text-muted-foreground">High quality, lossless</div>
                        </div>
                      </div>
                    </div>

                    {/* SVG Option */}
                    <div
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        downloadFormat === "svg"
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setDownloadFormat("svg")}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          downloadFormat === "svg"
                            ? "border-primary bg-primary"
                            : "border-gray-300"
                        }`}>
                          {downloadFormat === "svg" && (
                            <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm">SVG</div>
                          <div className="text-xs text-muted-foreground">Vector, infinitely scalable</div>
                        </div>
                      </div>
                    </div>

                    {/* JPEG Option */}
                    <div
                      className={`p-4 border-2 rounded-lg cursor-pointer transition-all ${
                        downloadFormat === "jpeg"
                          ? "border-primary bg-primary/5"
                          : "border-gray-200 hover:border-gray-300"
                      }`}
                      onClick={() => setDownloadFormat("jpeg")}
                    >
                      <div className="flex items-center gap-3">
                        <div className={`w-4 h-4 rounded-full border-2 ${
                          downloadFormat === "jpeg"
                            ? "border-primary bg-primary"
                            : "border-gray-300"
                        }`}>
                          {downloadFormat === "jpeg" && (
                            <div className="w-2 h-2 bg-white rounded-full m-0.5" />
                          )}
                        </div>
                        <div>
                          <div className="font-medium text-sm">JPEG</div>
                          <div className="text-xs text-muted-foreground">Smaller file size, compressed</div>
                        </div>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            )}


          </div>

          {/* Navigation */}
          <div className="flex flex-col md:flex-row md:justify-between items-center pt-8 mt-8 border-t border-gray-200">
            <Button
              variant="outline"
              onClick={() => setStep(step - 1)}
              disabled={step === 1}
              className="gap-2 h-11 px-6 border-slate-300 hover:bg-slate-50 disabled:opacity-40"
            >
              <ArrowLeft className="w-4 h-4" />
              Previous
            </Button>

              <span className="text-sm text-gray-500 font-medium">
                Step {step} of 3
              </span>
            <div className="flex  items-center gap-3">
              {step < 3 ? (
                <Button
                  onClick={() => {
                    if (!validateCurrentStep()) {
                      return;
                    }
                    setStep(step + 1);
                  }}
                  className="gap-2 h-11 px-6 bg-slate-800 hover:bg-slate-900 text-white font-medium"
                >
                  Continue Setup
                  <ArrowRight className="w-4 h-4" />
                </Button>
              ) : (
                <Button
                  onClick={() => {
                    if (!validateCurrentStep()) {
                      return;
                    }
                    handleDownload();
                  }}
                  className="gap-2 h-11 px-8 bg-teal-600 hover:bg-teal-700 text-white font-semibold shadow-lg"
                  disabled={
                    Object.keys(errors).length > 0 ||
                    downloadProgress.isDownloading ||
                    (isDynamicQR && customUrl.trim() !== "" && urlAvailability.available === false)
                  }
                >
                  {downloadProgress.isDownloading ? (
                    <>
                      <div className="w-4 h-4 border-2 border-white/30 border-t-white rounded-full animate-spin" />
                      {downloadProgress.step}
                    </>
                  ) : (
                    <>
                      <Download className="w-4 h-4" />
                      {session?.user && isDynamicQR ? "Download Trackable QR" : "Download QR Code"}
                    </>
                  )}
                </Button>
              )}
            </div>
          </div>

          {/* Download Progress */}
          {downloadProgress.isDownloading && (
            <div className="mt-4 p-4 bg-slate-50 rounded-lg border">
              <div className="flex items-center justify-between mb-2">
                <span className="text-sm font-medium text-slate-700">
                  {downloadProgress.step}
                </span>
                <span className="text-sm text-slate-500">
                  {downloadProgress.progress}%
                </span>
              </div>
              <Progress value={downloadProgress.progress} className="h-2" />
            </div>
          )}
        </div>

        {/* Right panel – preview */}
        <div className="xl:w-1/3 bg-gradient-to-br from-slate-50 to-slate-100 p-8 lg:p-12 border-l border-gray-100">
          <div className="sticky top-8">
            <div className="bg-white rounded-2xl shadow-lg border border-gray-200 overflow-hidden">
              <div className={`px-6 py-4 ${session?.user ? 'bg-gradient-to-r from-teal-600 to-teal-700' : 'bg-gradient-to-r from-red-600 to-red-700'}`}>
                <h3 className="text-lg font-semibold text-white">
                  {session?.user ? "Smart QR Preview" : "Blind QR Preview"}
                </h3>
                <p className="text-white/80 text-sm mt-1">
                  {session?.user ? "With real-time analytics" : "No tracking data available"}
                </p>
              </div>
              <div className="p-8">
                <div className="bg-gradient-to-br from-gray-50 to-white rounded-xl p-8 shadow-inner border-2 border-dashed border-gray-200">
                  <div ref={canvasRef} className="w-full max-w-64 h-64 mx-auto flex items-center justify-center" />
                </div>

                <div className="mt-6 space-y-3">
                  <div className="text-center">
                    {session?.user && isDynamicQR ? (
                      <div className="inline-flex items-center gap-2 px-3 py-1 bg-teal-50 text-teal-700 rounded-full text-xs font-medium border border-teal-200">
                        <div className="w-2 h-2 bg-teal-500 rounded-full animate-pulse"></div>
                        Trackable QR Code
                      </div>
                    ) : (
                      <div className="inline-flex items-center gap-2 px-3 py-1 bg-red-50 text-red-700 rounded-full text-xs font-medium border border-red-200">
                        <div className="w-2 h-2 bg-red-500 rounded-full animate-pulse"></div>
                        Blind QR Code
                      </div>
                    )}
                  </div>

                  {qrCodeName && (
                    <div className="text-center">
                      <p className="text-sm font-semibold text-slate-800">
                        {qrCodeName}
                      </p>
                    </div>
                  )}

                  <div className="flex flex-wrap justify-center gap-2">
                    {isDynamicQR && session?.user ? (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-teal-50 text-teal-700 rounded-md text-xs font-medium border border-teal-200">
                        <BarChart2 className="w-3 h-3" />
                        Analytics
                      </span>
                    ) : (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-red-50 text-red-700 rounded-md text-xs font-medium border border-red-200">
                        <AlertCircle className="w-3 h-3" />
                        No Analytics
                      </span>
                    )}
                    {options.image && (
                      <span className="inline-flex items-center gap-1 px-2 py-1 bg-slate-50 text-slate-700 rounded-md text-xs font-medium border border-slate-200">
                        <svg className="w-3 h-3" fill="currentColor" viewBox="0 0 20 20">
                          <path fillRule="evenodd" d="M4 3a2 2 0 00-2 2v10a2 2 0 002 2h12a2 2 0 002-2V5a2 2 0 00-2-2H4zm12 12H4l4-8 3 6 2-4 3 6z" clipRule="evenodd" />
                        </svg>
                        Logo
                      </span>
                    )}
                    <span className="inline-flex items-center gap-1 px-2 py-1 bg-slate-50 text-slate-700 rounded-md text-xs font-medium border border-slate-200">
                      2400×2400px
                    </span>
                  </div>

                  <div className="text-center pt-2">
                    <p className="text-xs text-gray-500">
                      {session?.user ? "Professional quality with tracking" : "High quality but no data"}
                    </p>
                  </div>

                  {!session?.user && (
                    <div className="bg-red-50 border border-red-200 rounded-lg p-3 mt-4">
                      <div className="text-center">
                        <AlertCircle className="w-4 h-4 text-red-600 mx-auto mb-1" />
                        <p className="text-xs text-red-700 font-medium">
                          Missing ROI Data
                        </p>
                        <p className="text-xs text-red-600 mt-1">
                          You won't know if this QR code works
                        </p>
                      </div>
                    </div>
                  )}
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  );
};

export default QRGenerator; 