---
// Header.astro
export interface Props {
  currentPath?: string;
}

const { currentPath = "/" } = Astro.props;

// Navigation items
const navItems = [
  { href: "/#features", label: "Features", external: false },
  { href: "/pricing", label: "Pricing", external: false },
  { href: "/tool/qr-code-generator", label: "QR Generator", external: false },
  // { href: "/docs", label: "Documentation", external: false },
];

// Check if current page matches nav item
const isActivePath = (href: string) => {
  if (href === "/" && currentPath === "/") return true;
  if (href !== "/" && currentPath?.startsWith(href)) return true;
  return false;
};
---

<header class="sticky top-0 z-50 w-full backdrop-blur-xl bg-white/95 border-b border-gray-200/60 shadow-sm">
  <div class="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
    <div class="flex justify-between items-center h-16 lg:h-18">
      
      <!-- Logo Section -->
      <a href="/" class="flex items-center space-x-3 group transition-transform duration-200 hover:-translate-y-0.5">
        <div class="relative w-10 h-10 bg-gradient-to-br from-blue-600 via-blue-700 to-indigo-700 rounded-xl flex items-center justify-center shadow-md group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">
          <div class="absolute inset-0 bg-gradient-to-br from-blue-500 to-indigo-600 rounded-xl blur opacity-30 group-hover:opacity-50 transition-opacity duration-300"></div>
          <svg class="relative w-5 h-5 text-white" fill="currentColor" viewBox="0 0 24 24" aria-hidden="true">
            <path d="M3 11h8V3H3v8zm2-6h4v4H5V5zM13 3v8h8V3h-8zm6 6h-4V5h4v4zM3 21h8v-8H3v8zm2-6h4v4H5v-4z"></path>
            <circle cx="17" cy="17" r="1"></circle>
            <circle cx="19" cy="17" r="1"></circle>
            <circle cx="17" cy="19" r="1"></circle>
            <circle cx="19" cy="19" r="1"></circle>
            <circle cx="21" cy="17" r="1"></circle>
            <circle cx="21" cy="19" r="1"></circle>
          </svg>
        </div>
        <div class="flex flex-col">
          <span class="font-bold text-lg text-gray-900 tracking-tight leading-5">QRAnalytica</span>
          <span class="text-xs text-gray-500 font-medium hidden sm:block leading-3">Analytics Platform</span>
        </div>
      </a>

      <!-- Desktop Navigation -->
      <nav class="hidden lg:flex items-center space-x-1" aria-label="Main navigation">
        {navItems.map((item) => (
          <a 
            href={item.href}
            class={`relative px-4 py-2 text-sm font-medium rounded-lg transition-all duration-200 group ${
              isActivePath(item.href) 
                ? 'text-blue-600 bg-blue-50/80' 
                : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
            }`}
          >
            {item.label}
            <span class={`absolute -bottom-1 left-1/2 transform -translate-x-1/2 h-0.5 bg-gradient-to-r from-blue-600 to-indigo-600 rounded-full transition-all duration-300 ${
              isActivePath(item.href) ? 'w-6' : 'w-0 group-hover:w-6'
            }`}></span>
          </a>
        ))}
      </nav>

      <!-- Desktop Actions -->
      <div class="flex items-center space-x-4">
        
        <!-- Desktop Auth Section -->
        <div class="hidden lg:flex items-center space-x-4" id="auth-section-desktop">
          <!-- Unauthenticated State -->
          <div id="unauthenticated-desktop" class="flex items-center space-x-4">
            <button 
              id="google-signin-desktop" 
              class="flex items-center space-x-2 px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 hover:shadow-sm transition-all duration-200"
              type="button"
            >
              <svg class="w-4 h-4" viewBox="0 0 24 24" aria-hidden="true">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"></path>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"></path>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"></path>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"></path>
              </svg>
              <span>Sign In</span>
            </button>
          </div>

          <!-- Authenticated State -->
          <div id="authenticated-desktop" class="hidden items-center space-x-3">
            <div class="flex items-center space-x-3 px-3 py-2 bg-gray-50 border border-gray-200 rounded-lg">
              <img 
                id="user-avatar-desktop" 
                src="" 
                alt="User" 
                class="w-8 h-8 rounded-full border-2 border-blue-500 object-cover"
              />
              <div class="flex flex-col">
                <span id="user-name-desktop" class="text-sm font-semibold text-gray-900"></span>
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-xs text-green-600 font-medium">Online</span>
                </div>
              </div>
            </div>
            <div class="flex items-center space-x-2">
              <a 
                href="/dashboard" 
                class="text-sm font-medium text-gray-600 hover:text-gray-900 px-3 py-2 rounded-lg hover:bg-gray-50 transition-colors duration-200"
              >
                Dashboard
              </a>
              <button 
                id="signout-desktop" 
                class="flex items-center space-x-1.5 text-sm font-medium text-gray-500 hover:text-red-600 px-3 py-2 rounded-lg hover:bg-red-50 transition-colors duration-200"
                type="button"
              >
                <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 16l4-4m0 0l-4-4m4 4H7m6 4v1a3 3 0 01-3 3H6a3 3 0 01-3-3V7a3 3 0 013-3h4a3 3 0 013 3v1"></path>
                </svg>
                <span>Sign Out</span>
              </button>
            </div>
          </div>
        </div>

        <!-- Primary CTA Button -->
        <a href="/tool/qr-code-generator" class="hidden sm:block">
          <button 
            class="flex items-center space-x-2 bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-5 py-2.5 rounded-lg font-semibold text-sm shadow-md hover:shadow-lg transform transition-all duration-200 hover:-translate-y-0.5"
            type="button"
          >
            <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
            </svg>
            <span>Create QR Code</span>
          </button>
        </a>

        <!-- Mobile Menu Button -->
        <button 
          id="mobile-menu-button" 
          class="lg:hidden inline-flex items-center justify-center p-2 rounded-lg text-gray-600 hover:text-gray-900 hover:bg-gray-50 border border-gray-200 hover:border-gray-300 transition-all duration-200"
          type="button"
          aria-expanded="false"
          aria-controls="mobile-menu"
          aria-label="Toggle navigation menu"
        >
          <svg class="w-5 h-5 transition-transform duration-200" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"></path>
          </svg>
        </button>
      </div>
    </div>

    <!-- Mobile Menu -->
    <div id="mobile-menu" class="lg:hidden hidden border-t border-gray-200 bg-white/95 backdrop-blur-lg">
      <div class="p-4 space-y-4">
        
        <!-- Mobile Navigation -->
        <nav class="space-y-1" aria-label="Mobile navigation">
          {navItems.map((item) => (
            <a 
              href={item.href}
              class={`block px-4 py-3 text-sm font-medium rounded-lg transition-all duration-200 ${
                isActivePath(item.href) 
                  ? 'text-blue-600 bg-blue-50' 
                  : 'text-gray-600 hover:text-gray-900 hover:bg-gray-50'
              }`}
            >
              {item.label}
            </a>
          ))}
        </nav>

        <!-- Mobile Auth Section -->
        <div class="border-t border-gray-200 pt-4 space-y-4">
          <!-- Unauthenticated State Mobile -->
          <div id="unauthenticated-mobile" class="space-y-3">
            <button 
              id="google-signin-mobile" 
              class="flex items-center justify-center space-x-2 w-full px-4 py-3 text-sm font-medium text-gray-700 bg-white border border-gray-200 rounded-lg hover:bg-gray-50 hover:border-gray-300 transition-all duration-200"
              type="button"
            >
              <svg class="w-4 h-4" viewBox="0 0 24 24" aria-hidden="true">
                <path fill="#4285F4" d="M22.56 12.25c0-.78-.07-1.53-.2-2.25H12v4.26h5.92c-.26 1.37-1.04 2.53-2.21 3.31v2.77h3.57c2.08-1.92 3.28-4.74 3.28-8.09z"></path>
                <path fill="#34A853" d="M12 23c2.97 0 5.46-.98 7.28-2.66l-3.57-2.77c-.98.66-2.23 1.06-3.71 1.06-2.86 0-5.29-1.93-6.16-4.53H2.18v2.84C3.99 20.53 7.7 23 12 23z"></path>
                <path fill="#FBBC05" d="M5.84 14.09c-.22-.66-.35-1.36-.35-2.09s.13-1.43.35-2.09V7.07H2.18C1.43 8.55 1 10.22 1 12s.43 3.45 1.18 4.93l2.85-2.22.81-.62z"></path>
                <path fill="#EA4335" d="M12 5.38c1.62 0 3.06.56 4.21 1.64l3.15-3.15C17.45 2.09 14.97 1 12 1 7.7 1 3.99 3.47 2.18 7.07l3.66 2.84c.87-2.6 3.3-4.53 6.16-4.53z"></path>
              </svg>
              <span>Sign In with Google</span>
            </button>
          </div>

          <!-- Authenticated State Mobile -->
          <div id="authenticated-mobile" class="hidden space-y-3">
            <div class="flex items-center space-x-3 p-3 bg-gray-50 border border-gray-200 rounded-lg">
              <img 
                id="user-avatar-mobile" 
                src="" 
                alt="User" 
                class="w-10 h-10 rounded-full border-2 border-blue-500 object-cover"
              />
              <div class="flex flex-col">
                <span id="user-name-mobile" class="text-sm font-semibold text-gray-900"></span>
                <div class="flex items-center space-x-1">
                  <div class="w-2 h-2 bg-green-500 rounded-full"></div>
                  <span class="text-xs text-green-600 font-medium">Online</span>
                </div>
              </div>
            </div>
            <div class="space-y-2">
              <a 
                href="/dashboard" 
                class="block text-center px-4 py-3 text-sm font-medium text-gray-600 hover:text-gray-900 border border-gray-200 rounded-lg hover:bg-gray-50 transition-all duration-200"
              >
                Dashboard
              </a>
              <button 
                id="signout-mobile" 
                class="w-full text-center px-4 py-3 text-sm font-medium text-gray-500 hover:text-red-600 border border-gray-200 rounded-lg hover:bg-red-50 transition-all duration-200"
                type="button"
              >
                Sign Out
              </button>
            </div>
          </div>

          <!-- Mobile CTA -->
          <a href="/tool/qr-code-generator" class="block">
            <button 
              class="flex items-center justify-center space-x-2 w-full bg-gradient-to-r from-blue-600 to-indigo-600 hover:from-blue-700 hover:to-indigo-700 text-white px-5 py-3 rounded-lg font-semibold text-sm shadow-md transition-all duration-200"
              type="button"
            >
              <svg class="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 4v16m8-8H4"></path>
              </svg>
              <span>Create QR Code</span>
            </button>
          </a>
        </div>
      </div>
    </div>
  </div>
</header>

<script>
  // Enhanced mobile menu and authentication functionality
  document.addEventListener("DOMContentLoaded", function () {
    const mobileMenuButton = document.getElementById("mobile-menu-button");
    const mobileMenu = document.getElementById("mobile-menu");

    // Authentication UI elements
    const unauthenticatedDesktop = document.getElementById("unauthenticated-desktop");
    const authenticatedDesktop = document.getElementById("authenticated-desktop");
    const unauthenticatedMobile = document.getElementById("unauthenticated-mobile");
    const authenticatedMobile = document.getElementById("authenticated-mobile");

    const googleSigninDesktop = document.getElementById("google-signin-desktop");
    const googleSigninMobile = document.getElementById("google-signin-mobile");
    const signoutDesktop = document.getElementById("signout-desktop");
    const signoutMobile = document.getElementById("signout-mobile");

    const userAvatarDesktop = document.getElementById("user-avatar-desktop");
    const userNameDesktop = document.getElementById("user-name-desktop");
    const userAvatarMobile = document.getElementById("user-avatar-mobile");
    const userNameMobile = document.getElementById("user-name-mobile");

    // Cookie utility
    function getCookie(name) {
      const cookies = document.cookie ? document.cookie.split("; ") : [];
      for (const c of cookies) {
        const [k, ...rest] = c.split("=");
        if (k === name) return rest.join("=");
      }
      return null;
    }

    // Check authentication state
    function checkAuthState() {
      try {
        const rawCookie = getCookie("user");
        if (rawCookie) {
          let jsonStr;
          try {
            const firstDecode = decodeURIComponent(rawCookie);
            jsonStr = decodeURIComponent(firstDecode);
          } catch {
            jsonStr = rawCookie;
          }

          const user = JSON.parse(jsonStr);
          return { authenticated: true, user };
        }
      } catch (error) {
        console.error("Error checking auth state:", error);
      }
      return { authenticated: false, user: null };
    }

    // Update UI based on auth state
    function updateAuthUI() {
      const authState = checkAuthState();

      if (authState.authenticated && authState.user) {
        // Show authenticated state
        unauthenticatedDesktop?.classList.add("hidden");
        authenticatedDesktop?.classList.remove("hidden");
        authenticatedDesktop?.classList.add("flex");
        unauthenticatedMobile?.classList.add("hidden");
        authenticatedMobile?.classList.remove("hidden");

        // Update user info
        const user = authState.user;
        if (userAvatarDesktop) userAvatarDesktop.src = user.picture || "/default-avatar.png";
        if (userNameDesktop) userNameDesktop.textContent = user.name || user.email;
        if (userAvatarMobile) userAvatarMobile.src = user.picture || "/default-avatar.png";
        if (userNameMobile) userNameMobile.textContent = user.name || user.email;
      } else {
        // Show unauthenticated state
        unauthenticatedDesktop?.classList.remove("hidden");
        authenticatedDesktop?.classList.add("hidden");
        authenticatedDesktop?.classList.remove("flex");
        unauthenticatedMobile?.classList.remove("hidden");
        authenticatedMobile?.classList.add("hidden");
      }
    }

    // Initial auth UI update
    updateAuthUI();

    // Sign in handlers
    function handleSignIn() {
      window.location.href = "/api/auth/google";
    }

    googleSigninDesktop?.addEventListener("click", handleSignIn);
    googleSigninMobile?.addEventListener("click", handleSignIn);

    // Sign out handlers
    function handleSignOut() {
      document.cookie = "user=; Max-Age=0; path=/";
      window.location.reload();
    }

    signoutDesktop?.addEventListener("click", handleSignOut);
    signoutMobile?.addEventListener("click", handleSignOut);

    // Mobile menu functionality
    if (mobileMenuButton && mobileMenu) {
      mobileMenuButton.addEventListener("click", function () {
        const isExpanded = mobileMenuButton.getAttribute("aria-expanded") === "true";
        const menuIcon = mobileMenuButton.querySelector("svg");

        // Toggle menu
        mobileMenuButton.setAttribute("aria-expanded", (!isExpanded).toString());
        mobileMenu.classList.toggle("hidden");

        // Animate icon
        if (menuIcon) {
          if (isExpanded) {
            menuIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>';
            menuIcon.style.transform = "rotate(0deg)";
          } else {
            menuIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"/>';
            menuIcon.style.transform = "rotate(90deg)";
          }
        }
      });

      // Close mobile menu when clicking outside
      document.addEventListener("click", function (event) {
        if (!mobileMenuButton.contains(event.target) && !mobileMenu.contains(event.target)) {
          mobileMenu.classList.add("hidden");
          mobileMenuButton.setAttribute("aria-expanded", "false");
          const menuIcon = mobileMenuButton.querySelector("svg");
          if (menuIcon) {
            menuIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>';
            menuIcon.style.transform = "rotate(0deg)";
          }
        }
      });

      // Close mobile menu on escape key
      document.addEventListener("keydown", function (event) {
        if (event.key === "Escape" && !mobileMenu.classList.contains("hidden")) {
          mobileMenu.classList.add("hidden");
          mobileMenuButton.setAttribute("aria-expanded", "false");
          const menuIcon = mobileMenuButton.querySelector("svg");
          if (menuIcon) {
            menuIcon.innerHTML = '<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M4 6h16M4 12h16M4 18h16"/>';
            menuIcon.style.transform = "rotate(0deg)";
          }
        }
      });
    }
  });
</script>